<?php
/**
 * Display attachments
 *
 * @package Tutor\Templates
 * @subpackage Single\Lesson
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

do_action( 'tutor_lesson/single/before/complete_form' );

$is_completed_lesson = tutor_utils()->is_completed_lesson();
$lesson_id = get_the_ID();
$button_class = $is_completed_lesson ? 'tutor-btn-success' : 'tutor-btn-primary';
$button_text = $is_completed_lesson ? esc_html__( 'Tamamlandı', 'tutor' ) : esc_html__( '<PERSON><PERSON> tama<PERSON>', 'tutor' );
?>
<div class="tutor-topbar-complete-btn tutor-mr-20">
	<button type="button"
		class="tutor-topbar-mark-btn tutor-btn <?php echo esc_attr( $button_class ); ?> tutor-ws-nowrap lesson-completion-btn"
		data-lesson-id="<?php echo esc_attr( $lesson_id ); ?>"
		data-completed="<?php echo $is_completed_lesson ? 'true' : 'false'; ?>">
		<span class="tutor-icon-circle-mark-line tutor-mr-8" area-hidden="true"></span>
		<span class="completion-btn-text"><?php echo $button_text; ?></span>
	</button>
</div>
<?php
do_action( 'tutor_lesson/single/after/complete_form' ); ?>
