"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[833],{64964:(l,e,c)=>{c.r(e);c.d(e,{default:()=>r});const r={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M24.195 9c-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15 8.285 0 15-6.716 15-15 0-8.284-6.715-15-15-15Zm-18 15c0-9.941 8.06-18 18-18 9.941 0 18 8.059 18 18s-8.059 18-18 18c-9.94 0-18-8.059-18-18Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M24.647 17.292a3.45 3.45 0 0 0-3.838 2.256 1.5 1.5 0 1 1-2.83-.996 6.45 6.45 0 0 1 12.535 2.149c-.001 2.446-1.816 4.074-3.143 4.96a13.118 13.118 0 0 1-2.805 1.402l-.018.006-.006.003h-.003l-.476-1.422.475 1.423a1.5 1.5 0 0 1-.952-2.845h.002l.002-.001.026-.01.126-.046a10.13 10.13 0 0 0 1.964-1.006c1.147-.765 1.807-1.611 1.807-2.465v-.002a3.45 3.45 0 0 0-2.866-3.406Zm-1.059 6.935Zm6.925-3.526v.001l-1.5-.002h1.5ZM22.695 32.25a1.5 1.5 0 0 1 1.5-1.5h.017a1.5 1.5 0 0 1 0 3h-.017a1.5 1.5 0 0 1-1.5-1.5Z" fill="currentColor"/>',viewBox:"0 0 48 48"}}}]);