<?php
/**
 * Dmr LMS ana sınıfı
 *
 * @package DmrLMS
 */

// <PERSON>ğrudan er<PERSON>im<PERSON> engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Eklenti sınıfı
 */
class Dmr_LMS {
    /**
     * Sınıf örneği
     *
     * @var object
     */
    private static $instance = null;

    /**
     * Singleton örneği oluştur
     *
     * @return object
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Yapılandırıcı
     */
    public function __construct() {
        // Tutor LMS yüklü ve aktif mi kontrol et
        add_action('admin_init', array($this, 'check_tutor_lms_dependency'));

        // Eklenti yüklendiğinde çalışacak kancalar
        add_action('plugins_loaded', array($this, 'load_plugin_textdomain'));
        add_action('plugins_loaded', array($this, 'init_plugin'));

        // Aktivasyon ve deaktivasyon kancaları
        register_activation_hook(DMR_LMS_FILE, array($this, 'activate'));
        register_deactivation_hook(DMR_LMS_FILE, array($this, 'deactivate'));
    }

    /**
     * Eklenti metinlerini çeviri için yükle
     */
    public function load_plugin_textdomain() {
        load_plugin_textdomain('dmr-lms', false, basename(dirname(DMR_LMS_FILE)) . '/languages');
    }

    /**
     * Tutor LMS bağımlılığını kontrol et
     */
    public function check_tutor_lms_dependency() {
        if (!function_exists('tutor') || !function_exists('tutor_utils')) {
            add_action('admin_notices', array($this, 'tutor_lms_missing_notice'));
            deactivate_plugins(plugin_basename(DMR_LMS_FILE));
            if (isset($_GET['activate'])) {
                unset($_GET['activate']);
            }
        }
    }

    /**
     * Tutor LMS eksik uyarısı
     */
    public function tutor_lms_missing_notice() {
        ?>
        <div class="notice notice-error is-dismissible">
            <p><?php _e('Dmr LMS eklentisi için Tutor LMS eklentisinin yüklü ve aktif olması gerekiyor.', 'dmr-lms'); ?></p>
        </div>
        <?php
    }

    /**
     * Eklentiyi başlat
     */
    public function init_plugin() {
        // Tutor LMS yüklü değilse işlemi durdur
        if (!function_exists('tutor')) {
            return;
        }

        // Şablon yollarını değiştir
        add_filter('tutor_get_template_path', array($this, 'override_template_path'), 999, 2);
    }

    /**
     * Şablon yolunu değiştir
     *
     * @param string $template_location Orijinal şablon yolu
     * @param string $template Şablon adı
     * @return string Değiştirilmiş şablon yolu
     */
    public function override_template_path($template_location, $template) {
        // Bizim şablon klasörümüzde bu şablon var mı kontrol et
        $custom_template = DMR_LMS_TEMPLATES_PATH . $template . '.php';

        if (file_exists($custom_template)) {
            return $custom_template;
        }

        return $template_location;
    }

    /**
     * Eklenti aktif edildiğinde çalışır
     */
    public function activate() {
        // Tutor LMS yüklü değilse uyarı ver ve işlemi durdur
        if (!function_exists('tutor')) {
            wp_die(__('Bu eklentiyi kullanmak için Tutor LMS eklentisinin yüklü ve aktif olması gerekiyor.', 'dmr-lms'), 'Plugin Activation Error', array('back_link' => true));
        }

        // Aktivasyon işlemleri
        do_action('dmr_lms_activated');
    }

    /**
     * Eklenti deaktif edildiğinde çalışır
     */
    public function deactivate() {
        // Deaktivasyon işlemleri
        do_action('dmr_lms_deactivated');
    }
}
