"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[6718],{82377:(a,h,v)=>{v.r(h);v.d(h,{default:()=>e});const e={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M10.778 5a.972.972 0 0 0-.972.972v.862a1.611 1.611 0 0 1-1.611 1.61H5.61a.11.11 0 0 0-.11.112v2.583a.111.111 0 0 0 .11.11h.861a2.472 2.472 0 1 1 0 4.946h-.86a.11.11 0 0 0-.111.11v2.584a.111.111 0 0 0 .11.11h2.584a.111.111 0 0 0 .11-.11v-.861a2.472 2.472 0 1 1 4.945 0v.86a.111.111 0 0 0 .111.111h2.583a.11.11 0 0 0 .111-.11v-2.584a1.611 1.611 0 0 1 1.611-1.611h.861a.972.972 0 0 0 0-1.944h-.86a1.611 1.611 0 0 1-1.612-1.611V8.556a.11.11 0 0 0-.11-.111H13.36a1.611 1.611 0 0 1-1.611-1.611v-.862a.972.972 0 0 0-.972-.971ZM5.61 6.945A1.611 1.611 0 0 0 4 8.556v2.583a1.611 1.611 0 0 0 1.611 1.611h.861a.972.972 0 1 1 0 1.944h-.86A1.611 1.611 0 0 0 4 16.305v2.584A1.611 1.611 0 0 0 5.611 20.5h2.584a1.611 1.611 0 0 0 1.611-1.611v-.861a.972.972 0 0 1 1.944 0v.86A1.611 1.611 0 0 0 13.36 20.5h2.583a1.611 1.611 0 0 0 1.612-1.611v-2.584a.11.11 0 0 1 .11-.11h.861a2.472 2.472 0 1 0 0-4.945h-.86a.111.111 0 0 1-.111-.111V8.556a1.611 1.611 0 0 0-1.612-1.612h-2.583a.111.111 0 0 1-.11-.11v-.862a2.472 2.472 0 0 0-4.946 0v.862a.11.11 0 0 1-.11.11H5.61Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);