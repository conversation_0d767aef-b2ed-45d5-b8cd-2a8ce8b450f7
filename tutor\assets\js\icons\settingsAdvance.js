"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[7660],{98383:(o,e,n)=>{n.r(e);n.d(e,{default:()=>r});const r={icon:'<path d="M7.744 3.238c.32-1.317 2.193-1.317 2.512 0a1.293 1.293 0 0 0 1.93.8c1.157-.706 2.482.619 1.777 1.777a1.293 1.293 0 0 0 .8 1.929c1.316.32 1.316 2.193 0 2.512a1.293 1.293 0 0 0-.8 1.93c.705 1.157-.62 2.482-1.778 1.777a1.293 1.293 0 0 0-1.929.8c-.32 1.316-2.193 1.316-2.512 0a1.293 1.293 0 0 0-1.93-.8c-1.157.705-2.482-.62-1.777-1.778a1.293 1.293 0 0 0-.8-1.929c-1.316-.32-1.316-2.193 0-2.512a1.293 1.293 0 0 0 .8-1.93c-.705-1.157.62-2.482 1.778-1.777a1.293 1.293 0 0 0 1.929-.8Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" fill="none" /><path d="M11.25 9a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0v0Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" fill="none" />',viewBox:"0 0 18 18"}}}]);