"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[5444],{16464:(C,o,r)=>{r.r(o);r.d(o,{default:()=>e});const e={icon:'<path d="M31.2367 14.1673C32.0387 13.2651 33.4221 11.9016 33.3486 11.3469C33.4054 10.8072 33.113 10.2993 32.5282 9.28513L31.7028 7.85323C31.0796 6.77053 30.7671 6.22913 30.2375 6.01533C29.7062 5.79813 29.1063 5.96853 27.9067 6.30933L25.8682 6.88243C25.0996 7.05953 24.2976 6.95933 23.5976 6.60013L23.0345 6.27593C22.4343 5.89113 21.973 5.32433 21.7179 4.65853L21.1598 2.99273C20.7922 1.89003 20.6084 1.33863 20.174 1.02113C19.7362 0.707032 19.1548 0.707031 17.9952 0.707031H16.1339C14.9727 0.707031 14.3929 0.707031 13.9568 1.02283C13.519 1.33863 13.3369 1.89003 12.9693 2.99273L12.4113 4.65853C12.1562 5.32433 11.6948 5.89113 11.0946 6.27593L10.5316 6.60013C9.83174 6.95903 9.02764 7.05963 8.26094 6.88413L6.22254 6.30933C5.02284 5.96853 4.42304 5.79813 3.89164 6.01363C3.36204 6.23083 3.04954 6.77053 2.42634 7.85323L1.60094 9.28513C1.01614 10.2993 0.723763 10.8056 0.782243 11.3469C0.837343 11.8866 1.22834 12.321 2.01194 13.1915L3.73294 15.118C4.15564 15.651 4.45474 16.58 4.45474 17.4154C4.45474 18.2508 4.15404 19.1798 3.73464 19.7128L2.01034 21.6393C1.22664 22.5082 0.835743 22.9442 0.778943 23.4839C0.722093 24.0236 1.01454 24.5315 1.59924 25.5457L2.42464 26.9777C3.04794 28.0604 3.36034 28.6017 3.89004 28.8156C4.42134 29.0328 5.02114 28.8624 6.22084 28.5215L8.25924 27.9484C9.02784 27.7713 9.82984 27.8716 10.5316 28.2308L11.093 28.5549C11.6928 28.9392 12.154 29.5073 12.4096 30.1723L12.9677 31.8398C13.3352 32.9426 13.5357 33.5157 13.8716 33.7563C13.9718 33.8281 15.1514 33.7863 16.1339 33.7563" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" fill="none"/><path d="M10.7969 16.9981C10.7969 13.5376 13.6021 10.7324 17.0626 10.7324C20.523 10.7324 23.3282 13.5376 23.3282 16.9981C23.3282 20.4585 20.523 23.2637 17.0626 23.2637C15.4024 23.2637 13.8931 22.6181 12.772 21.5642" stroke="currentColor" stroke-linecap="round" fill="none"/><path d="M21.772 17.207C21.772 14.7217 19.7573 12.707 17.272 12.707" stroke="currentColor" stroke-width="0.8" stroke-linecap="round" fill="none"/><path d="M11.0103 18.6191C11.1508 19.1436 11.3539 19.6346 11.6093 20.0863" stroke="currentColor" stroke-linecap="round" fill="none"/><path d="M36.8716 35.2909C37.4796 35.3031 38.0794 35.1506 38.6049 34.8502C39.0875 34.55 39.4895 34.1404 39.777 33.6558C40.0586 33.1654 40.2128 32.6144 40.2261 32.0516C40.25 31.4535 40.0938 30.8618 39.777 30.3504L38.6049 28.3574" stroke="currentColor" stroke-width="1.5" fill="none"/><path d="M40.2258 32.0511C40.2497 31.4531 40.0935 30.8614 39.7767 30.3499L31.9181 16.9871C31.626 16.4582 31.1827 16.0247 30.6428 15.7398C30.1383 15.471 29.5733 15.3301 28.9992 15.3301C28.4251 15.3301 27.8601 15.471 27.3556 15.7398C26.8257 16.0287 26.3921 16.4619 26.1072 16.9871L18.2217 30.3499C17.916 30.8659 17.7607 31.4544 17.7726 32.0511C17.7809 32.6147 17.9355 33.1668 18.2217 33.6554C18.5199 34.1421 18.9326 34.5516 19.4252 34.8498C19.9493 35.1496 20.5475 35.3021 21.1541 35.2905H36.8713C37.4793 35.3027 38.0791 35.1502 38.6046 34.8498C39.0872 34.5495 39.4892 34.1399 39.7767 33.6554C40.0583 33.1649 40.2125 32.614 40.2258 32.0511Z" fill="#FFB505"/><path d="M28.5959 28.8423L28.4039 20.5703H29.8439L29.6519 28.8423H28.5959ZM29.1239 30.1223C29.3159 30.1223 29.4759 30.1383 29.6039 30.1703C29.7426 30.1916 29.8492 30.261 29.9239 30.3783C29.9986 30.4956 30.0359 30.7036 30.0359 31.0023C30.0359 31.301 29.9986 31.5143 29.9239 31.6423C29.8492 31.7596 29.7426 31.829 29.6039 31.8503C29.4759 31.8823 29.3159 31.8983 29.1239 31.8983C28.9426 31.8983 28.7826 31.8823 28.6439 31.8503C28.5052 31.829 28.3986 31.7596 28.3239 31.6423C28.2492 31.5143 28.2119 31.301 28.2119 31.0023C28.2119 30.7036 28.2492 30.4956 28.3239 30.3783C28.3986 30.261 28.5052 30.1916 28.6439 30.1703C28.7826 30.1383 28.9426 30.1223 29.1239 30.1223Z" fill="currentColor" fill="none"/>',viewBox:"0 0 42 38"}}}]);