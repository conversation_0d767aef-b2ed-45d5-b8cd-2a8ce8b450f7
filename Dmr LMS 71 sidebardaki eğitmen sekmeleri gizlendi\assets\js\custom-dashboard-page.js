/**
 * Tutor LMS Custom Dashboard Page JavaScript
 * Bu dosya, dashboard ile ilgili tüm JavaScript işlevlerini birleştirir.
 *
 * İÇERİK:
 * 1. Avatar Dropdown Menü
 * 2. <PERSON><PERSON> Kaydırma <PERSON>ub<PERSON>ğu
 * 3. Dashboard Spotlight Modu
 * 4. Başlık Animasyonlarını Devre Dışı Bırakma
 * 5. Görünürlük Düzeltmeleri
 * 6. Tooltip Düzeltme
 */

(function($) {
    'use strict';

    // DOM yüklendiğinde çalışacak
    $(document).ready(function() {
        // Tüm işlevleri başlat
        initAvatarDropdown();
        initButtonFix();
        initCustomScrollbar();
        initDashboardSpotlight();
        initLoadTitleFix();
        initVisibilityFix();
        initTooltipFix();
        hideQnaTooltips();
        initColorVariables();
        initResponsiveContentArea();
        hideDashboardMenuItems();
        hideDashboardCards();
    });

    // Sayfa boyutu değiştiğinde responsive içerik alanını güncelle
    $(window).on('resize', function() {
        initResponsiveContentArea();
    });

    /********************************************
     * 1. AVATAR DROPDOWN MENÜ
     ********************************************/
    function initAvatarDropdown() {
        // Avatar elementini seç
        const avatarElement = $('.tutor-dashboard-header-avatar');

        // Eğer avatar elementi yoksa işlemi sonlandır
        if (!avatarElement.length) {
            return;
        }

        // Avatar elementine tıklama olayı ekle
        avatarElement.on('click', function(e) {
            e.stopPropagation();

            // Dropdown menüyü aç/kapat (toggle)
            $(this).find('.tutor-avatar-dropdown').toggleClass('show');
        });

        // Dropdown menü içindeki elementlere tıklama olayı ekle
        $('.tutor-avatar-dropdown-header, .tutor-avatar-dropdown-menu').on('click', function(e) {
            e.stopPropagation();
        });

        // Dropdown menü içindeki bağlantılara tıklama olayı ekle
        $('.tutor-avatar-dropdown-menu li a').on('click', function(e) {
            // Tıklamayı engelleme - normal bağlantı davranışını koru
            e.stopPropagation();
        });

        // Sayfa dışına tıklandığında dropdown menüyü kapat
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.tutor-dashboard-header-avatar').length) {
                $('.tutor-avatar-dropdown').removeClass('show');
            }
        });
    }


    /********************************************
     * 2. ÖZEL KAYDIRMA ÇUBUĞU
     ********************************************/
    function initCustomScrollbar() {
        // Basitleştirilmiş kaydırma çubuğu kontrolü
        function updateScrollbarVisibility() {
            const html = document.documentElement;
            const body = document.body;

            // Sayfanın toplam yüksekliği
            const scrollHeight = Math.max(
                body.scrollHeight,
                body.offsetHeight,
                html.clientHeight,
                html.scrollHeight,
                html.offsetHeight
            );

            // Görünür alan yüksekliği
            const clientHeight = html.clientHeight;

            // İçerik tam olarak görüntü alanına sığıyor mu?
            const isFullLength = scrollHeight <= clientHeight + 5; // 5px tolerans

            // HTML elementine sınıf ekle/kaldır
            if (isFullLength) {
                html.classList.add('scroll-at-max');
            } else {
                html.classList.remove('scroll-at-max');
            }
        }

        // Sayfa yüklendiğinde kontrol et
        updateScrollbarVisibility();

        // Sayfa kaydırıldığında kontrol et - throttle ile optimize edildi
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            if (!scrollTimeout) {
                scrollTimeout = setTimeout(function() {
                    updateScrollbarVisibility();
                    scrollTimeout = null;
                }, 100);
            }
        });

        // Sayfa boyutu değiştiğinde kontrol et - throttle ile optimize edildi
        let resizeTimeout;
        window.addEventListener('resize', function() {
            if (!resizeTimeout) {
                resizeTimeout = setTimeout(function() {
                    updateScrollbarVisibility();
                    resizeTimeout = null;
                }, 100);
            }
        });
    }
    /********************************************
     * 3. DASHBOARD SPOTLIGHT MODU
     ********************************************/
    function initDashboardSpotlight() {
        // Dashboard sayfasında olup olmadığımızı kontrol et
        if ($('.tutor-dashboard').length > 0) {
            // Body'ye tutor-dashboard-page sınıfını ekle
            $('body').addClass('tutor-dashboard-page');

            // Sayfa yüklendiğinde scroll'u en üste taşı
            window.scrollTo(0, 0);

            // Logo kısmı kaldırıldığı için bu kod artık gerekli değil
            // Avatar sidebar'a taşındı

            // Header avatar kısmı kaldırıldığı için bu kod artık gerekli değil
            // Avatar sidebar'a taşındı

            // Profil alanının görünürlüğünü sağla
            $('.tutor-dashboard-header, .tutor-frontend-dashboard-header, .tutor-header-left-side, .tutor-header-right-side').css({
                'display': 'flex',
                'visibility': 'visible',
                'opacity': '1'
            });

            $('.tutor-dashboard-header-avatar').css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            $('.tutor-user-info').css({
                'display': 'flex',
                'visibility': 'visible',
                'opacity': '1'
            });

            $('.tutor-dashboard-header-username, .tutor-dashboard-header-stats, .tutor-dashboard-header-ratings, .tutor-dashboard-header-display-name, .tutor-dashboard-header-greetings').css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });

            // Mobil menü toggle butonu ekle
            if ($('.tutor-dashboard-menu-toggler').length === 0) {
                $('.tutor-frontend-dashboard-header').prepend(
                    '<button class="tutor-dashboard-menu-toggler tutor-btn tutor-btn-outline-primary tutor-d-block tutor-d-lg-none">' +
                    '<i class="tutor-icon-hamburger-menu"></i>' +
                    '</button>'
                );

                // Mobil menü toggle işlevselliği
                $('.tutor-dashboard-menu-toggler').on('click', function() {
                    $('.tutor-dashboard-left-menu').toggleClass('show');
                });

                // Sayfa dışına tıklandığında menüyü kapat
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('.tutor-dashboard-left-menu').length &&
                        !$(e.target).closest('.tutor-dashboard-menu-toggler').length) {
                        $('.tutor-dashboard-left-menu').removeClass('show');
                    }
                });
            }

            // Tema tarafından eklenen gereksiz wrapper'ları düzelt
            setTimeout(function() {
                $('.tutor-dashboard').parents().each(function() {
                    // Ana içerik alanlarını tam genişliğe ayarla
                    if ($(this).hasClass('container') ||
                        $(this).hasClass('content-area') ||
                        $(this).hasClass('site-content') ||
                        $(this).hasClass('wrapper')) {
                        $(this).css({
                            'max-width': '100%',
                            'width': '100%',
                            'padding': '0',
                            'margin': '0'
                        });
                    }
                });
            }, 100);
        }
    }

    /********************************************
     * 4. BAŞLIK ANİMASYONLARINI DEVRE DIŞI BIRAKMA
     ********************************************/
    function initLoadTitleFix() {
        // Dashboard sayfasında olup olmadığımızı kontrol et
        if (document.querySelector('.tutor-dashboard') || document.body.classList.contains('tutor-dashboard-page')) {
            // CSS dosyasının yolunu oluştur
            if (typeof tutor_dashboard_spotlight_data !== 'undefined') {
                var cssPath = tutor_dashboard_spotlight_data.plugin_url + 'assets/css/disable-title-animations.css';

                // CSS dosyasını dinamik olarak ekle
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.type = 'text/css';
                link.href = cssPath;
                link.id = 'tutor-disable-title-animations-css';
                document.head.appendChild(link);
            }

            // Sayfa başlıklarını seç ve görünürlüğünü sağla
            var pageTitles = document.querySelectorAll('.tutor-fs-5, .tutor-dashboard-title, h1.tutor-fs-5, h2.tutor-fs-5, h3.tutor-fs-5, .header-title');

            pageTitles.forEach(function(title) {
                // Sadece görünürlük özelliklerini ayarla, diğer animasyonları koru
                title.style.opacity = '1';
                title.style.visibility = 'visible';
            });
        }
    }

    /********************************************
     * 5. GÖRÜNÜRLÜK DÜZELTMELERİ
     ********************************************/
    function initVisibilityFix() {
        // Dashboard sayfasında olup olmadığını kontrol et
        if (document.querySelector('.tutor-dashboard')) {
            // Sayfa tamamen yüklendikten sonra çalıştır
            setTimeout(function() {
                // CSS sınıfı ekleyerek görünürlük sorunlarını çöz
                document.body.classList.add('tutor-visibility-fixed');

                // Sadece ana elementleri seç, tüm alt elementleri değil
                const mainElements = [
                    '.tutor-frontend-dashboard-course-progress',
                    '.tutor-course-listing-item',
                    '.tutor-course-card',
                    '.tutor-dashboard-content-inner'
                ];

                // CSS seçicisini oluştur
                const selector = mainElements.join(', ');

                // Elementleri seç ve görünürlüğünü sağla
                document.querySelectorAll(selector).forEach(function(element) {
                    element.style.display = 'block';
                    element.style.opacity = '1';
                    element.style.visibility = 'visible';
                });

                // CSS ile görünürlük sorunlarını çözmek için stil ekle
                if (!document.getElementById('tutor-visibility-fix-style')) {
                    const style = document.createElement('style');
                    style.id = 'tutor-visibility-fix-style';
                    style.textContent = `
                        .tutor-visibility-fixed .tutor-dashboard-content-inner *,
                        .tutor-visibility-fixed .tutor-table,
                        .tutor-visibility-fixed .tutor-table * {
                            opacity: 1 !important;
                            visibility: visible !important;
                        }
                    `;
                    document.head.appendChild(style);
                }
            }, 100);
        }
    }

    /********************************************
     * 6. TOOLTIP DÜZELTME
     ********************************************/
    function initTooltipFix() {
        // Para çekme sayfasında olup olmadığını kontrol et
        if (document.querySelector('.tutor-frontend-dashboard-withdrawal')) {
            // Para çekme geçmişi tablosundaki tooltip'leri düzelt
            const tooltipWrappers = document.querySelectorAll('.withdraw-history-table-wrap .tooltip-wrap');

            tooltipWrappers.forEach(function(wrapper) {
                // Tooltip metnini bul
                const tooltipText = wrapper.querySelector('.tooltip-txt');

                if (tooltipText) {
                    // Varsayılan olarak gizli olmasını sağla
                    tooltipText.style.visibility = 'hidden';
                    tooltipText.style.opacity = '0';

                    // Hover olaylarını ekle
                    wrapper.addEventListener('mouseenter', function() {
                        tooltipText.style.visibility = 'visible';
                        tooltipText.style.opacity = '1';
                    });

                    wrapper.addEventListener('mouseleave', function() {
                        tooltipText.style.visibility = 'hidden';
                        tooltipText.style.opacity = '0';
                    });
                }
            });

            // Tooltip ikonlarını düzelt
            const tooltipIcons = document.querySelectorAll('.tooltip-icon');

            tooltipIcons.forEach(function(icon) {
                // Tooltip metnini bul
                const tooltipText = icon.querySelector('.tooltip-txt');

                if (tooltipText) {
                    // Varsayılan olarak gizli olmasını sağla
                    tooltipText.style.visibility = 'hidden';
                    tooltipText.style.opacity = '0';

                    // Hover olaylarını ekle
                    icon.addEventListener('mouseenter', function() {
                        tooltipText.style.visibility = 'visible';
                        tooltipText.style.opacity = '1';
                    });

                    icon.addEventListener('mouseleave', function() {
                        tooltipText.style.visibility = 'hidden';
                        tooltipText.style.opacity = '0';
                    });
                }
            });
        }
    }

    /********************************************
     * 7. SORU-CEVAP SAYFASI TOOLTIP GİZLEME
     ********************************************/
    function hideQnaTooltips() {
        // Soru-cevap sayfasında olup olmadığını kontrol et
        if (document.querySelector('.qna-list-table') ||
            document.querySelector('[data-qna_context]') ||
            window.location.href.includes('question-answer')) {

            // Sadece öğrenci sütunundaki (ilk sütun) QnA tooltip'lerini gizle
            const studentColumnTooltips = document.querySelectorAll('.qna-list-table td:first-child .tooltip-wrap.tooltip-icon-custom.tutor-qna-badges-wrapper');
            studentColumnTooltips.forEach(function(tooltip) {
                tooltip.style.display = 'none';
            });

            // Alternatif seçici - sadece tutor-qna-badges-wrapper sınıfına sahip olanları gizle
            const badgeTooltips = document.querySelectorAll('.qna-list-table .tutor-qna-badges-wrapper.tooltip-wrap');
            badgeTooltips.forEach(function(tooltip) {
                tooltip.style.display = 'none';
            });

            // Frontend dashboard tabloları için spesifik seçici
            const frontendTooltips = document.querySelectorAll('.frontend-dashboard-qna-table-instructor td:first-child .tooltip-wrap.tooltip-icon-custom.tutor-qna-badges-wrapper, .frontend-dashboard-qna-table-student td:first-child .tooltip-wrap.tooltip-icon-custom.tutor-qna-badges-wrapper');
            frontendTooltips.forEach(function(tooltip) {
                tooltip.style.display = 'none';
            });

            console.log('Sadece öğrenci sütunundaki QnA tooltip\'leri gizlendi');
        }

        // Dinamik içerik için MutationObserver ekle
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Yeni eklenen node'ları kontrol et
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // Sadece öğrenci sütunundaki QnA tooltip'lerini gizle
                            const studentTooltips = node.querySelectorAll('td:first-child .tooltip-wrap.tooltip-icon-custom.tutor-qna-badges-wrapper');
                            studentTooltips.forEach(function(tooltip) {
                                tooltip.style.display = 'none';
                            });

                            // Badge wrapper tooltip'lerini gizle
                            const badgeTooltips = node.querySelectorAll('.tutor-qna-badges-wrapper.tooltip-wrap');
                            badgeTooltips.forEach(function(tooltip) {
                                tooltip.style.display = 'none';
                            });
                        }
                    });
                }
            });
        });

        // Observer'ı başlat
        if (document.body) {
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    /********************************************
     * 8. RENK DEĞİŞKENLERİ VE GÖRÜNÜRLÜK
     ********************************************/
    function initColorVariables() {
        // Dashboard sayfasında olup olmadığını kontrol et
        if (document.querySelector('.tutor-dashboard')) {
            // Dashboard sayfasında olduğumuzu belirten bir sınıf ekle
            document.body.classList.add('tutor-modern-dashboard');

            // İçerik görünürlüğünü sağla - animasyonlar kaldırıldı
            document.querySelectorAll('.tutor-frontend-dashboard-course-progress, .tutor-course-listing-item, .tutor-card').forEach(item => {
                if (item) {
                    item.style.opacity = '1';
                    item.style.visibility = 'visible';
                    item.style.display = 'block';
                }
            });
        }
    }

    /********************************************
     * 9. RESPONSIVE İÇERİK ALANI
     ********************************************/
    function initResponsiveContentArea() {
        // Dashboard sayfasında olup olmadığını kontrol et
        if (document.querySelector('.tutor-dashboard')) {
            // Sayfa genişliğini al
            const windowWidth = $(window).width();

            // 991px ve altı için hiçbir şey yapma
            if (windowWidth <= 991) {
                // 991px ve altı için özel içerik alanı stillerini temizle
                const contentArea = $('#tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-frontend-dashboard-maincontent > div.tutor-col-12.tutor-col-md-8.tutor-col-lg-9 > div');
                if (contentArea.length) {
                    contentArea.css({
                        'max-width': '',
                        'padding-right': ''
                    });
                }
                return;
            }

            // 992px - 1920px arası için responsive içerik alanı
            if (windowWidth >= 992 && windowWidth <= 1920) {
                // Avatar elementini seç
                const avatarElement = $('.tutor-dashboard-header-avatar');
                const headerRightSide = $('.tutor-header-right-side');

                if (avatarElement.length && headerRightSide.length) {
                    // Avatar elementinin konumunu al
                    const avatarRight = windowWidth - (avatarElement.offset().left + avatarElement.outerWidth());

                    // İçerik alanını seç
                    const contentArea = $('#tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-frontend-dashboard-maincontent > div.tutor-col-12.tutor-col-md-8.tutor-col-lg-9 > div');

                    if (contentArea.length) {
                        // İçerik alanının sağ kenarını avatarın sağ kenarına hizala
                        contentArea.css({
                            'max-width': 'calc(100% - ' + avatarRight + 'px)',
                            'padding-right': (avatarRight + 30) + 'px' // 30px ekstra boşluk
                        });
                    }
                }
            }
        }
    }

    /********************************************
     * DASHBOARD MENÜ ÖĞELERİNİ GİZLE
     ********************************************/
    function hideDashboardMenuItems() {
        // Gizlenecek menü öğelerinin CSS seçicileri
        const hiddenMenuSelectors = [
            '.tutor-dashboard-menu-item.tutor-dashboard-menu-wishlist',
            '.tutor-dashboard-menu-item.tutor-dashboard-menu-reviews',
            '.tutor-dashboard-menu-item.tutor-dashboard-menu-purchase_history',
            '.tutor-dashboard-menu-item.tutor-dashboard-menu-my-courses',
            '.tutor-dashboard-menu-item.tutor-dashboard-menu-announcements',
            '.tutor-dashboard-menu-item.tutor-dashboard-menu-withdraw',
            '.tutor-dashboard-menu-item.tutor-dashboard-menu-quiz-attempts',
            '.tutor-dashboard-menu-divider-header'
        ];

        // Her bir menü öğesini gizle
        hiddenMenuSelectors.forEach(function(selector) {
            $(selector).hide();
        });

        // Alternatif olarak, menü öğelerini class adına göre de gizle
        $('.tutor-dashboard-permalinks li').each(function() {
            const $menuItem = $(this);
            const menuLink = $menuItem.find('a').attr('href');

            if (menuLink) {
                // URL'de belirtilen sayfa adları geçiyorsa gizle
                if (menuLink.includes('wishlist') ||
                    menuLink.includes('reviews') ||
                    menuLink.includes('purchase_history') ||
                    menuLink.includes('my-courses') ||
                    menuLink.includes('announcements') ||
                    menuLink.includes('withdraw') ||
                    menuLink.includes('quiz-attempts')) {
                    $menuItem.hide();
                }
            }
        });

        // Divider header'ları da gizle
        $('.tutor-dashboard-menu-divider-header').hide();
    }

    /********************************************
     * DASHBOARD BİLGİ KARTLARINI VE TABLOLARINI GİZLE
     ********************************************/
    function hideDashboardCards() {
        // Sadece eğitmen kartlarını gizle (4., 5., 6. kartlar - Total Students, Total Courses, Total Earnings)
        $('.tutor-dashboard-content-inner .tutor-row.tutor-gx-lg-4 .tutor-col-lg-6.tutor-col-xl-4').each(function(index) {
            // Index 3, 4, 5 (4., 5., 6. kartlar) eğitmen kartları
            if (index >= 3) {
                $(this).hide();
            }
        });

        // İkon bazında eğitmen kartlarını gizle
        $('.tutor-icon-user-graduate').closest('.tutor-col-lg-6.tutor-col-xl-4').hide(); // Total Students
        $('.tutor-icon-box-open').closest('.tutor-col-lg-6.tutor-col-xl-4').hide(); // Total Courses
        $('.tutor-icon-coins').closest('.tutor-col-lg-6.tutor-col-xl-4').hide(); // Total Earnings

        // Kurslarım tablosunu ve başlığını gizle
        $('.popular-courses-heading-dashboard').hide();
        $('.table-popular-courses').hide();
        $('.tutor-table.table-popular-courses').hide();

        // "My Courses" başlığını içeren div'i gizle
        $('.tutor-fs-5.tutor-fw-medium.tutor-color-black').each(function() {
            if ($(this).text().includes('My Courses') || $(this).text().includes('Kurslarım')) {
                $(this).closest('.popular-courses-heading-dashboard').hide();
            }
        });

        // Tablo içeren parent container'ı gizle
        $('.tutor-table-responsive').each(function() {
            if ($(this).find('.table-popular-courses').length > 0) {
                $(this).hide();
            }
        });
    }
})(jQuery);
