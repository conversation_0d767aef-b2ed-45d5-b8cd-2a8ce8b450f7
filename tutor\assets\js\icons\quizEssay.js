"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[2495],{42030:(C,t,V)=>{V.r(t);V.d(t,{default:()=>H});const H={icon:'<rect width="32" height="32" rx="4" fill="#FFCCCD"/><path d="M25.0876 20.8514C25.1536 20.9012 25.2044 20.9685 25.2342 21.0456C25.2739 21.1355 25.2942 21.2327 25.2937 21.3309C25.2954 21.4243 25.2792 21.5171 25.2461 21.6044C25.2151 21.6798 25.1645 21.7455 25.0995 21.7946L21.7268 25.1079C21.6478 25.2047 21.5392 25.2729 21.4177 25.302C21.2846 25.3288 21.1465 25.315 21.0213 25.2624C20.8948 25.2239 20.7855 25.1427 20.7122 25.0326C20.6404 24.9133 20.6059 24.7753 20.6131 24.6362V22.6546H11.3115V24.6362C11.3188 24.7753 11.2843 24.9133 11.2125 25.0326C11.1392 25.1427 11.0299 25.2239 10.9033 25.2624C10.7894 25.3278 10.6554 25.3489 10.5268 25.3219C10.3981 25.2927 10.2849 25.2164 10.2098 25.1079L6.86088 21.7788C6.80011 21.7209 6.75041 21.6524 6.71424 21.5767C6.68243 21.4987 6.66627 21.4152 6.66668 21.3309C6.66459 21.2388 6.68075 21.1473 6.71424 21.0614C6.74561 20.9852 6.79612 20.9183 6.86088 20.8672L10.2098 17.5184C10.2877 17.4211 10.397 17.3539 10.5189 17.3281C10.6515 17.2991 10.7899 17.3115 10.9152 17.3638C11.0421 17.4039 11.1513 17.4865 11.2243 17.5976C11.2966 17.7167 11.3312 17.8549 11.3234 17.9939V19.9755H20.6369V17.9939C20.6292 17.8549 20.6637 17.7167 20.736 17.5976C20.8091 17.4865 20.9183 17.4039 21.0451 17.3638C21.1574 17.3017 21.2882 17.282 21.4137 17.3083C21.543 17.3412 21.6584 17.4149 21.7426 17.5184L25.0876 20.8514ZM7.34439 6.66715C7.24833 6.66367 7.15258 6.67985 7.063 6.71471C6.99034 6.7479 6.92238 6.79055 6.86088 6.84153C6.79232 6.89828 6.74485 6.97646 6.72613 7.06347C6.69803 7.15456 6.68465 7.24955 6.6865 7.34486V11.3318C6.68483 11.4167 6.69824 11.5013 6.72613 11.5815C6.75257 11.6629 6.79888 11.7365 6.86088 11.7955C6.9104 11.8591 6.97815 11.9061 7.05508 11.9303C7.14857 11.9598 7.2464 11.9732 7.34439 11.9699H8.81473C8.89965 11.9716 8.98419 11.9581 9.06441 11.9303C9.14584 11.9038 9.21939 11.8575 9.27842 11.7955C9.34205 11.746 9.38905 11.6782 9.41317 11.6013C9.44183 11.5144 9.45524 11.4233 9.4528 11.3318V9.35023H14.387V14.6767H12.9761C12.889 14.6774 12.8029 14.6949 12.7224 14.7283C12.6438 14.7617 12.5725 14.8102 12.5124 14.8709C12.4453 14.923 12.3966 14.9952 12.3737 15.077C12.3496 15.1621 12.3376 15.2502 12.338 15.3386V16.6544C12.3376 16.7389 12.3496 16.823 12.3737 16.9041C12.4025 16.9853 12.45 17.0586 12.5124 17.1181C12.5629 17.1807 12.6283 17.2298 12.7026 17.2607C12.7896 17.2953 12.8825 17.3128 12.9761 17.3123H18.9724C19.0595 17.3116 19.1456 17.2942 19.226 17.2607C19.3053 17.2264 19.3778 17.1781 19.44 17.1181C19.5017 17.0667 19.5482 16.9996 19.5748 16.9239C19.6013 16.8365 19.6147 16.7457 19.6144 16.6544V15.2792C19.6138 15.1931 19.6004 15.1076 19.5748 15.0255C19.5458 14.9465 19.4998 14.8748 19.44 14.8155C19.388 14.7483 19.3157 14.6997 19.2339 14.6767C19.1488 14.6527 19.0608 14.6407 18.9724 14.6411H17.5615V9.29871H22.5551V11.2803C22.5534 11.3652 22.5668 11.4498 22.5947 11.53C22.6197 11.6121 22.6662 11.686 22.7295 11.744C22.7805 11.8088 22.8474 11.8593 22.9237 11.8906C23.0095 11.9241 23.1011 11.9403 23.1932 11.9382H24.6437C24.7303 11.9406 24.8165 11.9245 24.8965 11.891C24.9764 11.8576 25.0483 11.8074 25.1074 11.744C25.1725 11.695 25.2209 11.6272 25.2461 11.5498C25.2714 11.4623 25.2835 11.3714 25.2818 11.2803V7.31711C25.2819 7.2313 25.2699 7.14591 25.2461 7.06347C25.2181 6.98185 25.1704 6.90836 25.1074 6.84946C25.0549 6.78015 24.9811 6.73002 24.8973 6.70678C24.8129 6.67951 24.7245 6.66612 24.6358 6.66715H7.34439Z" fill="#FF686B"/>',viewBox:"0 0 32 32"}}}]);