"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[4235],{62118:(l,C,e)=>{e.r(C);e.d(C,{default:()=>r});const r={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M4.26351 5.11995C4.75237 4.63109 5.41541 4.35645 6.10677 4.35645H7.96354C8.37775 4.35645 8.71354 4.69223 8.71354 5.10645C8.71354 5.52066 8.37775 5.85645 7.96354 5.85645H6.10677C5.81324 5.85645 5.53172 5.97305 5.32417 6.18061C5.11661 6.38817 5 6.66968 5 6.96322V18.1038C5 18.3974 5.11661 18.6789 5.32417 18.8864C5.53173 19.094 5.81324 19.2106 6.10677 19.2106H11.3958C11.81 19.2106 12.1458 19.5464 12.1458 19.9606C12.1458 20.3748 11.81 20.7106 11.3958 20.7106H6.10677C5.41541 20.7106 4.75237 20.436 4.26351 19.9471C3.77464 19.4582 3.5 18.7952 3.5 18.1038V6.96322C3.5 6.27186 3.77464 5.60881 4.26351 5.11995Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M17.2473 12.7119C17.6615 12.7119 17.9973 13.0477 17.9973 13.4619V16.4255H20.9609C21.3751 16.4255 21.7109 16.7612 21.7109 17.1755C21.7109 17.5897 21.3751 17.9255 20.9609 17.9255H17.2473C16.8331 17.9255 16.4973 17.5897 16.4973 17.1755V13.4619C16.4973 13.0477 16.8331 12.7119 17.2473 12.7119Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M12.7839 5.10645C12.7839 4.69223 13.1197 4.35645 13.5339 4.35645H15.3907C16.0821 4.35645 16.7451 4.63109 17.234 5.11995C17.7228 5.60881 17.9975 6.27186 17.9975 6.96322V10.6768C17.9975 11.091 17.6617 11.4268 17.2475 11.4268C16.8333 11.4268 16.4975 11.091 16.4975 10.6768V6.96322C16.4975 6.66968 16.3809 6.38817 16.1733 6.18061C15.9658 5.97305 15.6842 5.85645 15.3907 5.85645H13.5339C13.1197 5.85645 12.7839 5.52066 12.7839 5.10645Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M9.82039 4C9.20914 4 8.71362 4.49552 8.71362 5.10677C8.71362 5.71802 9.20914 6.21354 9.82039 6.21354H11.6772C12.2884 6.21354 12.7839 5.71802 12.7839 5.10677C12.7839 4.49552 12.2884 4 11.6772 4H9.82039ZM7.21362 5.10677C7.21362 3.66709 8.38071 2.5 9.82039 2.5H11.6772C13.1168 2.5 14.2839 3.66709 14.2839 5.10677C14.2839 6.54645 13.1168 7.71354 11.6772 7.71354H9.82039C8.38071 7.71354 7.21362 6.54645 7.21362 5.10677Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M17.2475 14.2119C15.6108 14.2119 14.2839 15.5387 14.2839 17.1755C14.2839 18.8122 15.6108 20.139 17.2475 20.139C18.8842 20.139 20.211 18.8122 20.211 17.1755C20.211 15.5387 18.8842 14.2119 17.2475 14.2119ZM12.7839 17.1755C12.7839 14.7103 14.7823 12.7119 17.2475 12.7119C19.7126 12.7119 21.711 14.7103 21.711 17.1755C21.711 19.6406 19.7126 21.639 17.2475 21.639C14.7823 21.639 12.7839 19.6406 12.7839 17.1755Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M7.21362 10.6768C7.21362 10.2625 7.54941 9.92676 7.96362 9.92676H11.6772C12.0914 9.92676 12.4272 10.2625 12.4272 10.6768C12.4272 11.091 12.0914 11.4268 11.6772 11.4268H7.96362C7.54941 11.4268 7.21362 11.091 7.21362 10.6768Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M7.21362 14.3906C7.21362 13.9764 7.54941 13.6406 7.96362 13.6406H10.7488C11.163 13.6406 11.4988 13.9764 11.4988 14.3906C11.4988 14.8048 11.163 15.1406 10.7488 15.1406H7.96362C7.54941 15.1406 7.21362 14.8048 7.21362 14.3906Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);