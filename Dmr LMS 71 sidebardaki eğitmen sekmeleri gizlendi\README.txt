=== Dmr LMS ===
Contributors: dmrdeveloper
Tags: lms, eğiti<PERSON>, kurs, tutor lms, öğrenme yönetim sistemi, elearning, online kurs
Requires at least: 5.3
Tested up to: 6.7
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Dmr LMS, Tutor LMS eklentisini geliştiren, modern ve kullanıcı dostu bir öğrenme deneyimi sunan premium bir eklentidir.

== Açıklama ==

Dmr LMS, Tutor LMS eklentisinin kurs izleme ekranı ve dashboard tasarımını tamamen yeniden düzenleyen, kullanıcı deneyimini en üst düzeye çıkaran bir eklentidir. Orjinal Tutor LMS eklentisi ile birlikte çalışarak, eğitim platformunuzu daha profesyonel ve kullanıcı dostu hale getirir.

<PERSON><PERSON> eklenti, eğ<PERSON><PERSON><PERSON> ve öğrenciler için optimize edilmiş arayüzler sunar, sayfa yükleme hızını artırır ve mobil cihazlarda mükemmel bir deneyim sağlar.

= Neden Dmr LMS? =

* **Modern Tasarım**: Çağdaş ve profesyonel bir görünüm için tamamen yenilenmiş arayüz
* **Hızlı Performans**: Optimize edilmiş kodlar sayesinde daha hızlı sayfa yükleme süreleri
* **Mobil Uyumluluk**: Tüm cihazlarda mükemmel görünen responsive tasarım
* **Kolay Kullanım**: Sezgisel ve kullanıcı dostu arayüz
* **SEO Dostu**: Arama motorları için optimize edilmiş yapı

= Öne Çıkan Özellikler =

* **Gelişmiş Video Oynatıcı**: Özelleştirilmiş kontroller, tam ekran modu ve hız ayarları
* **Spotlight Modu**: Dikkat dağıtıcı öğeleri gizleyerek odaklanmayı artıran özel mod
* **Hızlı Ders Tamamlama**: Tek tıklamayla ders tamamlama ve ilerleme takibi
* **Gelişmiş Yorum Sistemi**: Daha interaktif ve kullanışlı yorum sistemi
* **Akıllı İçerik Arama**: Kurs içeriğinde hızlı arama yapabilme
* **Özelleştirilmiş Dashboard**: Daha kullanışlı ve bilgilendirici dashboard ekranı
* **Otomatik Kaydırma**: Uzun içeriklerde otomatik kaydırma özelliği
* **Görsel İyileştirmeler**: Daha iyi renk şemaları ve tipografi

= Veri Entegrasyonu =

Dmr LMS, Tutor LMS'in altyapısını kullanarak verileri şu şekilde entegre eder:

1. **Kurs İçerikleri**: Tutor LMS API'si üzerinden kurs içeriklerini çeker
2. **Kullanıcı Verileri**: WordPress kullanıcı sistemi ve Tutor LMS meta verileri ile entegre çalışır
3. **İlerleme Takibi**: Tutor LMS'in ilerleme takip sistemini geliştirir ve optimize eder
4. **Medya İçerikleri**: Video, ses ve diğer medya içeriklerini optimize ederek sunar

= Teknik Özellikler =

* **Şablon Geçersiz Kılma Sistemi**: Tutor LMS şablonlarını dinamik olarak geçersiz kılar
* **Önbellek Optimizasyonu**: Daha hızlı yükleme için akıllı önbellek sistemi
* **Lazy Loading**: Görüntüler ve videolar için gecikmeli yükleme
* **AJAX Tabanlı İşlemler**: Sayfa yenileme gerektirmeyen AJAX tabanlı işlemler
* **Responsive Framework**: Bootstrap ve modern CSS teknikleri ile responsive tasarım
* **SEO Optimizasyonu**: Arama motorları için optimize edilmiş yapı ve semantik HTML
* **Çoklu Dil Desteği**: Tam çeviri desteği ve RTL uyumluluğu

== Kurulum ==

1. Eklentiyi `/wp-content/plugins/` dizinine yükleyin veya WordPress yönetici panelinden "Eklenti Ekle" bölümünü kullanarak yükleyin
2. WordPress yönetici panelinden "Eklentiler" menüsünden eklentiyi etkinleştirin
3. Tutor LMS eklentisinin yüklü ve etkin olduğundan emin olun
4. Eklenti otomatik olarak çalışmaya başlayacak ve Tutor LMS arayüzünü geliştirecektir

= Minimum Gereksinimler =

* WordPress 5.3 veya üzeri
* PHP 7.4 veya üzeri
* Tutor LMS 2.0.0 veya üzeri
* Modern bir web tarayıcısı (Chrome, Firefox, Safari, Edge)

== Sık Sorulan Sorular ==

= Dmr LMS, Tutor LMS'in tüm özellikleriyle uyumlu mu? =

Evet, Dmr LMS, Tutor LMS'in tüm özellikleriyle tam uyumlu çalışacak şekilde tasarlanmıştır. Tutor LMS'in herhangi bir özelliğini devre dışı bırakmaz, sadece kullanıcı deneyimini geliştirir.

= Eklenti mevcut kurslarımı ve verilerimi etkiler mi? =

Hayır, Dmr LMS mevcut kurslarınızı veya verilerinizi değiştirmez. Sadece bu verilerin sunulma şeklini ve kullanıcı arayüzünü geliştirir.

= Eklentiyi kaldırdığımda ne olur? =

Eklentiyi kaldırdığınızda, Tutor LMS'in orijinal arayüzüne geri dönersiniz. Kurslarınız, öğrencileriniz ve diğer verileriniz etkilenmez.

= Dmr LMS, Tutor LMS'in gelecek sürümleriyle uyumlu olacak mı? =

Evet, Dmr LMS düzenli olarak güncellenir ve Tutor LMS'in yeni sürümleriyle uyumluluğu sağlanır.

= Eklenti mobil cihazlarda nasıl çalışır? =

Dmr LMS, mobil cihazlar için özel olarak optimize edilmiştir. Responsive tasarımı sayesinde tüm ekran boyutlarında mükemmel görünür ve çalışır.

= Spotlight modu nedir ve nasıl çalışır? =

Spotlight modu, öğrencilerin dikkatini dağıtabilecek sayfa öğelerini gizleyerek, sadece ders içeriğine odaklanmalarını sağlayan özel bir moddur. Bu mod, öğrenme verimliliğini artırmak için tasarlanmıştır.

= Eklenti hangi tarayıcılarla uyumludur? =

Dmr LMS, tüm modern web tarayıcılarıyla (Chrome, Firefox, Safari, Edge) uyumludur. Internet Explorer gibi eski tarayıcılarda bazı görsel özellikler sınırlı olabilir.

= Eklenti çoklu dil desteği sunuyor mu? =

Evet, Dmr LMS tam çeviri desteği sunar ve .pot dosyaları içerir. İstediğiniz dile çevirebilirsiniz.

== Ekran Görüntüleri ==

1. Özelleştirilmiş kurs izleme ekranı - Modern ve kullanıcı dostu arayüz
2. Gelişmiş video oynatıcı - Tam ekran modu ve özel kontroller
3. Mobil görünüm - Tüm cihazlarda mükemmel deneyim
4. Dashboard ekranı - Bilgilendirici ve kullanışlı dashboard
5. Spotlight modu - Odaklanmış öğrenme deneyimi
6. Yorum sistemi - Gelişmiş etkileşim özellikleri

== Değişiklik Günlüğü ==

= 1.0.0 (07.05.2025) =
* İlk resmi sürüm
* Tutor LMS ile tam entegrasyon
* Gelişmiş video oynatıcı
* Spotlight modu
* Responsive tasarım
* SEO optimizasyonları
* Performans iyileştirmeleri

== Yükseltme Bildirimi ==

Eklentiyi yükseltmeden önce sitenizin tam yedeğini almanız önerilir. Yükseltme işlemi genellikle sorunsuz gerçekleşir, ancak her zaman bir yedek bulundurmak iyi bir uygulamadır.

== Teknik Destek ==

Premium teknik destek için [destek portalımızı](https://example.com/support) ziyaret edebilirsiniz. Destek ekibimiz, eklentiyle ilgili tüm sorularınıza yardımcı olmak için hazırdır.

Ayrıca [bilgi tabanımızda](https://example.com/docs) eklentinin kullanımı hakkında detaylı dökümanlar bulabilirsiniz.

== Geliştirici Bilgileri ==

Dmr LMS, deneyimli WordPress geliştiricileri tarafından, en iyi kodlama uygulamaları ve WordPress standartları gözetilerek geliştirilmiştir. Eklenti düzenli olarak güncellenir ve yeni özellikler eklenir.

== Lisans ==

Dmr LMS, GNU Genel Kamu Lisansı v2 veya sonraki sürümleri altında lisanslanmıştır. Bu, eklentiyi kullanabilir, değiştirebilir ve dağıtabilirsiniz, ancak aynı lisans altında paylaşmanız gerekir.
