"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[2390],{72581:(C,e,H)=>{H.r(e);H.d(e,{default:()=>l});const l={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M6.55556 5.75C6.34191 5.75 6.13701 5.83487 5.98594 5.98594C5.83487 6.13701 5.75 6.34191 5.75 6.55556V17.4444C5.75 17.6581 5.83487 17.863 5.98594 18.0141C6.13701 18.1651 6.34191 18.25 6.55556 18.25H7.3613V12.7778C7.3613 12.3636 7.69708 12.0278 8.1113 12.0278H15.8891C16.3033 12.0278 16.6391 12.3636 16.6391 12.7778V18.25H17.4444C17.6581 18.25 17.863 18.1651 18.0141 18.0141C18.1651 17.863 18.25 17.6581 18.25 17.4444V9.19955L14.8005 5.75H8.8613V8.13889H14.3335C14.7477 8.13889 15.0835 8.47468 15.0835 8.88889C15.0835 9.3031 14.7477 9.63889 14.3335 9.63889H8.1113C7.69708 9.63889 7.3613 9.3031 7.3613 8.88889V5.75H6.55556ZM8.1113 4.25H6.55556C5.94408 4.25 5.35766 4.49291 4.92528 4.92528C4.49291 5.35766 4.25 5.94408 4.25 6.55556V17.4444C4.25 18.0559 4.49291 18.6423 4.92528 19.0747C5.35766 19.5071 5.94408 19.75 6.55556 19.75H17.4444C18.0559 19.75 18.6423 19.5071 19.0747 19.0747C19.5071 18.6423 19.75 18.0559 19.75 17.4444V8.88889C19.75 8.68998 19.671 8.49921 19.5303 8.35856L15.6414 4.46967C15.5008 4.32902 15.31 4.25 15.1111 4.25H8.1113ZM15.1391 18.25H8.8613V13.5278H15.1391V18.25Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);