"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[2874],{30281:(C,t,H)=>{H.r(t);H.d(t,{default:()=>L});const L={icon:'<rect width="32" height="32" rx="4" fill="#ABC4FF"/><path d="M10.8976 6.87863C10.9477 6.81217 11.0138 6.75954 11.0898 6.72569C11.1726 6.68472 11.2641 6.66453 11.3564 6.66687C11.45 6.66497 11.5427 6.68512 11.627 6.72569C11.7023 6.76065 11.7682 6.81308 11.8192 6.87863L15.1486 10.1962C15.2506 10.2807 15.3245 10.3942 15.3603 10.5217C15.3847 10.6535 15.3711 10.7895 15.3211 10.9139C15.2829 11.0402 15.2009 11.1487 15.0897 11.2198C14.9713 11.2898 14.835 11.3238 14.6976 11.3178H12.7172V24.6275C12.7202 24.725 12.7042 24.8223 12.6702 24.9137C12.6366 24.9934 12.5872 25.0655 12.5251 25.1255C12.4666 25.1869 12.3939 25.2327 12.3133 25.2588C12.2273 25.2872 12.1371 25.3005 12.0466 25.2981H10.7251C10.6308 25.2996 10.5368 25.2864 10.4466 25.2588C10.3637 25.2328 10.2884 25.1871 10.227 25.1255C10.1656 25.0664 10.1186 24.994 10.0898 24.9137C10.0625 24.8208 10.0493 24.7243 10.0506 24.6275V11.3217H8.02706C7.8897 11.3269 7.7537 11.2929 7.63491 11.2237C7.52491 11.1514 7.4432 11.0433 7.40354 10.9178C7.33696 10.7984 7.31695 10.6587 7.34736 10.5254C7.37778 10.3922 7.45647 10.2749 7.56824 10.1962L10.8976 6.87863ZM20.023 14.4393C19.9898 14.4923 19.9473 14.5388 19.8975 14.5766C19.8625 14.6114 19.8194 14.6371 19.772 14.6511C19.7261 14.6639 19.6786 14.6705 19.6309 14.6707H18.1172C18.0375 14.6726 17.9584 14.6566 17.8858 14.6237C17.8225 14.5902 17.7696 14.54 17.7328 14.4786C17.6827 14.4179 17.6502 14.3446 17.6387 14.2668C17.6278 14.1824 17.6414 14.0966 17.6779 14.0197L20.0505 7.03157C20.0806 6.92682 20.1457 6.83551 20.2348 6.77275C20.3211 6.71329 20.4242 6.68306 20.5289 6.68648H22.1759C22.2784 6.68185 22.3794 6.71228 22.4622 6.77275C22.5482 6.83699 22.6103 6.92807 22.6387 7.03157L25.0308 14.0197C25.0475 14.0986 25.0475 14.1801 25.0308 14.259C25.0146 14.3353 24.9883 14.4092 24.9524 14.4786C24.9057 14.5385 24.8469 14.5879 24.7798 14.6237C24.7139 14.6558 24.6414 14.6719 24.5681 14.6707H23.0779C22.9668 14.6763 22.8573 14.643 22.7681 14.5766C22.687 14.499 22.6333 14.3972 22.6152 14.2864L22.223 13.1099H20.4544L20.0897 14.2864C20.0759 14.3407 20.0534 14.3923 20.023 14.4393ZM24.3798 18.9805C24.4058 18.9382 24.4244 18.8918 24.4347 18.8432C24.4489 18.7935 24.4555 18.742 24.4543 18.6903V17.8315C24.455 17.7654 24.4417 17.6999 24.4151 17.6393C24.3899 17.5744 24.3511 17.5155 24.3014 17.4668C24.2574 17.4203 24.2056 17.3818 24.1485 17.353C24.0888 17.3239 24.0227 17.3104 23.9563 17.3138H18.8073C18.7412 17.3131 18.6757 17.3265 18.6152 17.353C18.5502 17.3783 18.4914 17.417 18.4426 17.4668C18.3914 17.5181 18.3513 17.5795 18.325 17.6472C18.297 17.7114 18.2836 17.781 18.2858 17.8511V19.055C18.2881 19.1107 18.3014 19.1653 18.325 19.2158C18.3825 19.3359 18.4794 19.4327 18.5995 19.4903C18.6637 19.5182 18.7334 19.5316 18.8034 19.5295H21.1367L18.2662 23.6079C18.227 23.6463 18.1951 23.6915 18.1721 23.7412C18.1467 23.7897 18.1333 23.8434 18.1328 23.8981V24.8157C18.1329 24.8817 18.1463 24.9471 18.1721 25.0079C18.1973 25.0728 18.236 25.1316 18.2858 25.1804C18.3328 25.2269 18.3871 25.2653 18.4466 25.2941C18.5114 25.3202 18.5806 25.3336 18.6505 25.3334H24.0544C24.1189 25.3317 24.1827 25.3184 24.2426 25.2941C24.3073 25.2684 24.366 25.2297 24.4151 25.1804C24.4637 25.1328 24.5035 25.0771 24.5328 25.0157C24.5575 24.952 24.5695 24.884 24.5681 24.8157V23.6118C24.5674 23.5462 24.5555 23.4812 24.5328 23.4196C24.5046 23.3565 24.4647 23.2993 24.4151 23.251C24.3689 23.2026 24.3145 23.1628 24.2544 23.1334C24.1908 23.1076 24.1229 23.0943 24.0544 23.0942H21.525L21.6034 22.9804L24.3798 18.9805ZM21.6348 11.0511L21.3681 10.0943L21.0701 11.0511H21.6348Z" fill="#3E64DE"/>',viewBox:"0 0 32 32"}}}]);