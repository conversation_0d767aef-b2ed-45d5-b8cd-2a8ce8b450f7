// Custom Rating Script
document.addEventListener('DOMContentLoaded', function() {
    // Tüm rating count elementlerini bul
    const ratingCountElements = document.querySelectorAll('.tutor-ratings-count');
    
    // Her bir rating count elementini düzenle
    ratingCountElements.forEach(function(element) {
        // Mevcut içeriği al
        const text = element.textContent;
        
        // İçerik içinde "Ratings" kelimesini ara ve kaldır
        if (text.includes('Ratings')) {
            // Sadece sayıyı ve parantezleri tut
            const numberMatch = text.match(/\((\d+)\s+Ratings\)/);
            if (numberMatch && numberMatch[1]) {
                // Sadece sayıyı parantez içinde göster
                element.textContent = '(' + numberMatch[1] + ')';
            }
        }
    });
});
